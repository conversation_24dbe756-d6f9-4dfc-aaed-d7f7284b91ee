<script setup lang="ts">
import { computed, ref, useTemplateRef, watch } from 'vue';
import { deepClone, isNone, renderLabel } from '@/script';
import type { AnyIndicatorRiskParamObject, MarketValueConfig } from '@/types/riskc';
import { IdcComponentNameDef } from '../../ComponentNameDef';
import { RiskSummarizationMethod } from '../../RiskParamTypeAndSummary';
import { AlertType, AlertTypes, ExpressionType } from '@/enum/riskc';
import { DefaultRiskParamCreator } from '../../DefaultRiskParamCreator';

const localExpressionTypes = [
  ExpressionType.GreaterThan,
  ExpressionType.GreaterEqual,
  ExpressionType.LessThan,
  ExpressionType.LessEqual,
];

const localAlertTypes = [AlertType.Ignore, AlertType.Warning, AlertType.Prevention];

/**
 * 期货市值占比
 */
interface RuleInnerSetting extends AnyIndicatorRiskParamObject {
  paramAlert: MarketValueConfig;
  paramBlock: MarketValueConfig;
}

const { ruleSetting } = defineProps<{
  ruleSetting: RuleInnerSetting | null | undefined;
}>();

const $form = useTemplateRef('$form');
const localRuleSetting = ref<RuleInnerSetting>(createEmptyRiskParam());

const definedActions = computed(() => {
  const { paramAlert, paramBlock } = localRuleSetting.value as RuleInnerSetting;
  return [paramAlert, paramBlock];
});

const rule1 = [{ required: true, message: '', trigger: 'blur', validator: validatePart1 }];

const rule2 = [{ required: true, message: '', trigger: 'blur', validator: validatePart2 }];

const errorNotice = ref('');
const errorNotice2 = ref('');

function makeFormItemLabel(alertType: number) {
  return renderLabel(alertType, AlertTypes) + '设置';
}

function validatePart1(rule: any, value: any, callback: any) {
  const failed = definedActions.value.some(item => {
    const { navExpression, nav } = item;
    if (isNone(navExpression) || isNone(nav)) {
      return true;
    } else {
      return false;
    }
  });
  if (failed) {
    errorNotice.value = '请设置净值比较条件';
    callback(new Error(errorNotice.value));
  } else {
    errorNotice.value = '';
    callback();
  }
}

function validatePart2(rule: any, value: any, callback: any) {
  const failed = definedActions.value.some(item => {
    const { expression, value, alertType } = item;
    if (isNone(expression) || isNone(value) || isNone(alertType)) {
      return true;
    } else {
      return false;
    }
  });
  if (failed) {
    errorNotice2.value = '请完善期货持仓规模条件';
    callback(new Error(errorNotice2.value));
  } else {
    errorNotice2.value = '';
    callback();
  }
}

watch(
  () => ruleSetting,
  newValue => {
    localRuleSetting.value = handleInputChange(newValue);
  },
  { immediate: true },
);

function handleInputChange(newValue: RuleInnerSetting | null | undefined) {
  return newValue && JSON.stringify(newValue) != '{}'
    ? deepClone(newValue)
    : createEmptyRiskParam();
}

function createEmptyRiskParam(): RuleInnerSetting {
  return DefaultRiskParamCreator[IdcComponentNameDef.FuturesPositionRatio]();
}

function validate() {
  return $form.value!.validate();
}

const emitter = defineEmits<{
  riskParamChanged: [descrption: string | string[]];
}>();

function handleParamHotChange() {
  emitter('riskParamChanged', getRiskParamSummary());
}

function getRiskParamSummary() {
  return RiskSummarizationMethod[IdcComponentNameDef.FuturesPositionRatio](localRuleSetting.value);
}
function getRiskSetting() {
  return deepClone(localRuleSetting.value);
}

defineExpose({
  validate,
  getRiskSetting,
  getRiskParamSummary,
});
</script>

<template>
  <div class="rule-tmpl" h-full pr-12>
    <el-form ref="$form" :model="localRuleSetting" label-width="80px">
      <div class="custom-row">
        <el-form-item label="指标设置">
          <label class="placed-label">
            <span>单一合约持仓量/该合约总持仓</span>
            <span pl-30 color-red>{{ errorNotice || errorNotice2 }}</span>
          </label>
        </el-form-item>
      </div>
      <template v-for="(item, idx) in definedActions" :key="idx">
        <div class="custom-row">
          <el-form-item :label="makeFormItemLabel(item.alertType)" prop="part1" :rules="rule1">
            <div w-full flex aic gap-10>
              <label class="placed-label">当净值</label>
              <el-select
                v-model="item.navExpression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in localExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="3"
                :step="0.001"
                :min="0"
                v-model="item.nav"
                @change="handleParamHotChange"
                style="width: 100px"
              ></el-input-number>
              <label class="placed-label">时，该指标</label>
            </div>
          </el-form-item>
          <el-form-item label="" prop="part2" :rules="rule2">
            <div w-full flex aic gap-10>
              <el-select
                v-model="item.expression"
                style="width: 100px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in localExpressionTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-input-number
                :controls="false"
                :precision="0"
                :step="1"
                :min="0"
                v-model="item.value"
                @change="handleParamHotChange"
                style="width: 80px"
              ></el-input-number>
              <label class="placed-label">万元时</label>
              <el-select
                v-model="item.alertType"
                style="width: 140px"
                @change="handleParamHotChange"
              >
                <el-option
                  v-for="(item, idx) in localAlertTypes"
                  :key="idx"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<style scoped>
.rule-tmpl {
  .placed-label {
    color: var(--g-text-color-2);
  }

  :deep() {
    .el-form-item__label {
      color: var(--g-text-color-2);
    }
  }

  .custom-row {
    margin-bottom: 18px;

    :deep() {
      > .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .post-item {
    :deep() {
      > .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
