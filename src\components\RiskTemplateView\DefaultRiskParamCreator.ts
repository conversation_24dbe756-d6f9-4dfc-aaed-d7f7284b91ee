import { formatDateTime, getUser } from '@/script';
import type { RiskRule, RiskRuleConfiguration } from '../../../../xtrade-sdk/dist';
import { IdcComponentNameDef } from './ComponentNameDef';

import {
  AlertType,
  BetweenExpressionType,
  ExpressionType,
  FuturesMarginRatioType,
  MarketValueRatioType,
  MarketValueTypes,
  NavValueStopLossType,
  PriceDeviationType,
  RiskBsFlag,
  RiskPriceType,
  RiskStatisticsType,
  RiskStepControl,
  UnlimitedExpressionType,
} from '@/enum/riskc';

const Defs = IdcComponentNameDef;
export const DefaultRiskParamCreator: { [componentName: string]: () => any } = {
  [Defs.BlackList]: function EmptyBlackListRiskParam() {
    return {
      classType: Defs.BlackList,
      alertType: AlertType.Warning.value,
    };
  },

  [Defs.WhiteList]: function () {
    return {
      classType: Defs.WhiteList,
      alertType: AlertType.Warning.value,
    };
  },

  [Defs.SingleOrderMaxVolume]: function () {
    return {
      classType: Defs.SingleOrderMaxVolume,
      bsFlag: RiskBsFlag.Buy.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 1000,
        alertType: AlertType.Ignore.value,
      },
    };
  },

  [Defs.AccountOrderMaxVolume]: function () {
    return {
      classType: Defs.AccountOrderMaxVolume,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 1000,
        alertType: AlertType.Ignore.value,
      },
    };
  },

  [Defs.SingleOrderMaxAmount]: function () {
    return {
      classType: Defs.SingleOrderMaxAmount,
      bsFlag: RiskBsFlag.Buy.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 1000,
        alertType: AlertType.Ignore.value,
      },
    };
  },

  [Defs.NetBuyAmount]: function () {
    return {
      classType: Defs.NetBuyAmount,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 1000,
        alertType: AlertType.Ignore.value,
      },
    };
  },

  [Defs.TradeFrequency]: function () {
    return {
      classType: Defs.TradeFrequency,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        second: 1,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 1000,
        alertType: AlertType.Ignore.value,
        second: 1,
      },
    };
  },

  [Defs.OrderCancelRate]: function () {
    return {
      classType: Defs.OrderCancelRate,
      expression: ExpressionType.GreaterThan.value,
      orderCount: 500,
      riskStatisticsType: RiskStatisticsType.Summary.value,
      paramAlert: {
        alertType: AlertType.Warning.value,
        expression: ExpressionType.GreaterThan.value,
        value: 20,
      },
      paramForbidCancel: {
        alertType: AlertType.Ignore.value,
        expression: ExpressionType.GreaterThan.value,
        value: 30,
      },
    };
  },

  [Defs.InvalidOrderRate]: function () {
    return {
      classType: Defs.InvalidOrderRate,
      expression: ExpressionType.GreaterThan.value,
      orderCount: 500,
      riskStatisticsType: RiskStatisticsType.Summary.value,
      paramAlert: {
        alertType: AlertType.Warning.value,
        expression: ExpressionType.GreaterThan.value,
        value: 20,
      },
      paramForbidOrder: {
        alertType: AlertType.ForbiddenOfCancel.value,
        expression: ExpressionType.GreaterThan.value,
        value: 30,
      },
    };
  },

  [Defs.IndayReversedDirection]: function () {
    return {
      classType: Defs.SelfTrade,
      intraDayReversalAlert: AlertType.Warning.value,
    };
  },

  [Defs.SelfTrade]: function () {
    return {
      classType: Defs.SelfTrade,
      selfTradeAlert: AlertType.Warning.value,
    };
  },

  [Defs.PriceDeviation]: function () {
    return {
      classType: Defs.PriceDeviation,
      deviationType: PriceDeviationType.LatestPrice.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 2,
        alertType: AlertType.Warning.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 5,
        alertType: AlertType.Ignore.value,
      },
    };
  },

  [Defs.PriceLimit]: function () {
    const { MarketPrice, CeilingPrice, FloorPrice } = RiskPriceType;
    return {
      classType: Defs.PriceLimit,
      marketPrice: MarketPrice.value,
      upPrice: CeilingPrice.value,
      downPrice: FloorPrice.value,
    };
  },

  [Defs.MarketValue]: function () {
    const localMarketValueTypes = [...MarketValueTypes];
    return {
      classType: Defs.MarketValue,
      riskStatisticsType: RiskStatisticsType.Summary.value,
      marketValueType: localMarketValueTypes[0].value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        nav: 1.5,
        navExpression: localMarketValueTypes[0].value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Ignore.value,
        nav: 1.5,
        navExpression: localMarketValueTypes[0].value,
      },
    };
  },

  [Defs.MarketValueRatio]: function () {
    return {
      classType: Defs.MarketValueRatio,
      riskStatisticsType: RiskStatisticsType.Summary.value,
      marketValueType: MarketValueRatioType.MARKET_VALUE.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        nav: 1.5,
        navExpression: MarketValueRatioType.MARKET_VALUE.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Ignore.value,
        nav: 1.5,
        navExpression: MarketValueRatioType.MARKET_VALUE.value,
      },
    };
  },

  [Defs.MarketCapitalRatio]: function () {
    return {
      classType: Defs.MarketCapitalRatio,
      riskStatisticsType: RiskStatisticsType.SingleInstrument.value,
      marketValueType: MarketValueRatioType.MARKET_VALUE.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        nav: 1.5,
        navExpression: MarketValueRatioType.MARKET_VALUE.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Ignore.value,
        nav: 1.5,
        navExpression: MarketValueRatioType.MARKET_VALUE.value,
      },
    };
  },

  [Defs.FlowableMarketCapitalRatio]: function () {
    return {
      classType: Defs.FlowableMarketCapitalRatio,
      riskStatisticsType: RiskStatisticsType.SingleInstrument.value,
      marketValueType: MarketValueRatioType.MARKET_VALUE.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        nav: 1.5,
        navExpression: MarketValueRatioType.MARKET_VALUE.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Ignore.value,
        nav: 1.5,
        navExpression: MarketValueRatioType.MARKET_VALUE.value,
      },
    };
  },

  [Defs.FuturesMargin]: function () {
    return {
      classType: Defs.FuturesMargin,
      riskStatisticsType: RiskStatisticsType.Summary.value,
      marketValueType: FuturesMarginRatioType.MARKET_VALUE.value,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        nav: 1.5,
        navExpression: FuturesMarginRatioType.MARKET_VALUE.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Ignore.value,
        nav: 1.5,
        navExpression: FuturesMarginRatioType.MARKET_VALUE.value,
      },
    };
  },

  [Defs.NavStopLoss]: function () {
    const localExpressionTypes = [
      UnlimitedExpressionType,
      ExpressionType.GreaterThan,
      ExpressionType.GreaterEqual,
      BetweenExpressionType,
      ExpressionType.LessThan,
      ExpressionType.LessEqual,
    ];
    return {
      classType: Defs.NavStopLoss,
      alertType: AlertType.Warning.value,
      expression: localExpressionTypes[0].value,
      netValueStopLossType: NavValueStopLossType.WARNING_LINE.value,
      value: 1.3,
      minValue: 0.9,
      maxValue: 1.5,
    };
  },

  [Defs.FuturesPositionRatio]: function () {
    return {
      classType: Defs.FuturesPositionRatio,
      paramAlert: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Warning.value,
        nav: 1.5,
        navExpression: FuturesMarginRatioType.MARKET_VALUE.value,
      },
      paramBlock: {
        expression: ExpressionType.GreaterThan.value,
        value: 500,
        alertType: AlertType.Ignore.value,
        nav: 1.5,
        navExpression: FuturesMarginRatioType.MARKET_VALUE.value,
      },
    };
  },
};

export function assignDefaultRuleConfig(indicator_id: number) {
  const blackListConfig: RiskRuleConfiguration = {
    indicatorId: indicator_id || (null as any),
    kindCodes: [],
    baseConditions: [],
    riskParam: null,
  };
  return blackListConfig;
}

export function createEmptyRule(
  template_id: number,
  idc_id: number,
  idc_name: string,
  is_black_list = false,
) {
  const user = getUser()!;
  const date = new Date();
  const now = date.getTime();
  const today_num = Number(formatDateTime(now, 'yyyyMMdd'));
  const rname = `${idc_name}_${formatDateTime(date, 'yyMMdd')}_${formatDateTime(date, 'hhmmss')}`;
  const { instruction, entrusting, progressing, marketClosed } = RiskStepControl;

  const checkObject =
    is_black_list === true
      ? instruction.value | entrusting.value | progressing.value | marketClosed.value
      : entrusting.value;

  const rule: RiskRule = {
    id: null as any,
    templateId: template_id,
    ruleName: rname,
    indicatorId: idc_id,
    configuration: assignDefaultRuleConfig(idc_id),
    beginTime: 93000,
    endTime: 150000,
    beginDay: today_num,
    endDay: today_num,
    checkObject,
    checkInterval: 60,
    active: true,
    createUserId: user.userId,
    createUser: user.username,
    orgId: user.orgId,
    orgName: user.orgName,
    createTime: now,
    updateTime: now,
  };

  return rule;
}
