<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { onMounted, shallowRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import type { CashInfo } from '../../../../../xtrade-sdk';
import { Repos } from '../../../../../xtrade-sdk/dist';
import { cashRecordColumns } from './shared/columnDefinitions';

const recordsRepo = new Repos.RecordsRepo();

// 定义组件属性
const { activeItem, refreshKey } = defineProps<{
  activeItem?: AccountInfo;
  refreshKey: number;
}>();

// 基础列定义
const columns: ColumnDefinition<CashInfo> = [
  cashRecordColumns.tradingDay,
  cashRecordColumns.identityName,
  cashRecordColumns.inMoney,
  cashRecordColumns.outMoney,
  cashRecordColumns.operatorUserName,
  cashRecordColumns.createTime,
  cashRecordColumns.type,
];

// 出入金数据
const records = shallowRef<CashInfo[]>([]);

// 获取出入金数据
const fetchRecords = async () => {
  if (!activeItem) return;
  const resp = await recordsRepo.getIoMoney(activeItem.id, '');
  if (resp && resp.data) {
    records.value = resp.data;
  } else {
    records.value = [];
  }
};

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchRecords();
    }
  },
  { deep: true },
);

// 监听refreshKey变化
watch(
  () => refreshKey,
  () => {
    if (activeItem) {
      fetchRecords();
    }
  },
);

onMounted(() => {
  if (activeItem) {
    fetchRecords();
  }
});
</script>

<template>
  <div flex="~ col" h-full>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
      :columns
      :data="records"
      flex-1
      min-h-1
    ></VirtualizedTable>
  </div>
</template>

<style scoped></style>
