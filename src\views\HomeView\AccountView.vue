<script setup lang="ts">
import AccountList from '@/components/AccountView/AccountList.vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab } from '@/types';
import { computed, ref, useTemplateRef } from 'vue';
import type { LegacyAccountInfo } from '../../../../xtrade-sdk/dist';
import { hasPermission } from '@/script';
import { MenuPermitAccountManagement } from '@/enum';

const activeAccount = ref<LegacyAccountInfo | null>(null);
const componentTabs = useTemplateRef('componentTabs');
const refreshKey = ref(0);

const tabs = computed<ComponentTab[]>(() => [
  {
    label: '当日订单',
    component: 'TodayOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '当日持仓',
    component: 'TodayPositions',
    show: () => hasPermission(MenuPermitAccountManagement.查看持仓),
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '当日成交',
    component: 'TodayRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史订单',
    component: 'HistoryOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史持仓',
    component: 'HistoryPositions',
    show: () => hasPermission(MenuPermitAccountManagement.查看持仓),
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史成交',
    component: 'HistoryRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史权益',
    component: 'HistoryEquity',
    show: () => hasPermission(MenuPermitAccountManagement.查看权益),
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '出入金',
    component: 'CashRecord',
    props: {
      activeItem: activeAccount.value,
      refreshKey: refreshKey.value,
    },
  },
]);

const handleRowClick = (row: LegacyAccountInfo) => {
  activeAccount.value = row;
};

const handleRefresh = () => {
  refreshKey.value++;
};
</script>

<template>
  <div>
    <el-splitter layout="vertical">
      <el-splitter-panel>
        <AccountList h-full @row-click="handleRowClick" @refresh="handleRefresh" />
      </el-splitter-panel>
      <el-splitter-panel>
        <ComponentTabs ref="componentTabs" :tabs h-full />
      </el-splitter-panel>
    </el-splitter>
  </div>
</template>

<style scoped></style>
