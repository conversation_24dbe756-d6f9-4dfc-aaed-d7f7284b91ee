<template>
  <div class="product-tutorial" :class="{ is_backward: hasInternalDialog }">
    <el-tabs v-if="contextProduct" v-model="activeTab" class="typical-tabs">
      <el-tab-pane :name="tabs.product">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-consist"></i>
              <span pl-8>产品信息</span>
            </span>
          </slot>
        </template>
        <ProductBasicInfoForm
          v-model="contextProduct"
          @save="basicSaved"
          @cancel="cancelEditBasic"
          @refresh="handleRefresh"
        ></ProductBasicInfoForm>
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission(MenuPermitProductManagement.账号配置)" :name="tabs.account">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-document-code"></i>
              <span pl-8>账号管理</span>
            </span>
          </slot>
        </template>
        <AccountBinding v-model="contextProduct" />
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission(MenuPermitProductManagement.人员配置)" :name="tabs.user">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-user"></i>
              <span pl-8>人员设置</span>
            </span>
          </slot>
        </template>
        <ProductUserBind :product="contextProduct" @refresh="handleRefresh"></ProductUserBind>
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission(MenuPermitProductManagement.风控配置)" :name="tabs.risk">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-bell"></i>
              <span pl-8>风控设置</span>
            </span>
          </slot>
        </template>
        <RuleOverview
          :product-id="contextProduct?.id"
          @dialog="val => (hasInternalDialog = val)"
        ></RuleOverview>
      </el-tab-pane>
    </el-tabs>
    <ProductBasicInfoForm
      v-else
      @save="basicSaved"
      @cancel="cancelEditBasic"
      @refresh="handleRefresh"
    ></ProductBasicInfoForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ProductBasicInfoForm from './ProductBasicInfoForm.vue';
import AccountBinding from './AccountBinding.vue';
import ProductUserBind from './ProductUserBind.vue';
import RuleOverview from '../RiskTemplateView/EntityEntrance/RuleOverview.vue';
import type { ProductInfo } from '@/types';
import { hasPermission } from '@/script';
import { MenuPermitProductManagement } from '@/enum';

const contextProduct = defineModel<ProductInfo | null>();
const tabs = { product: 'product', account: 'account', user: 'user', risk: 'risk' };
const activeTab = ref(tabs.product);
const hasInternalDialog = ref(false);

const emitter = defineEmits<{
  cancel: [];
  save: [data: ProductInfo];
  refresh: [];
}>();

function basicSaved(row: ProductInfo) {
  emitter('save', row);
}

function cancelEditBasic() {
  emitter('cancel');
}

function handleRefresh() {
  emitter('refresh');
}
</script>

<style scoped>
.product-tutorial {
  min-height: 500px;
  &.is_backward {
    height: 900px;
  }
}
</style>
