import { BaseRepo } from '../modules/base-repo';
import {
  EntityRiskTemplateInfo,
  KindOfAsset,
  RiskIndicator,
  RiskMessage,
  RiskRule,
  RiskTemplate,
  SystemIdentityInfo,
} from '../types/table/risk-control';

const BaseUrl = '../v4/risk';

export class RiskControlRepo extends BaseRepo {
  constructor() {
    super();
  }

  /**
   * 查询所有风控模板
   */
  async QueryTemplates() {
    return await this.assist.Get<RiskTemplate[]>(`${BaseUrl}/template/all`);
  }

  /**
   * 创建风控模板
   */
  async CreateTemplate(template: RiskTemplate) {
    return await this.assist.Post<[]>(`${BaseUrl}/template`, {}, template);
  }

  /**
   * 更新风控模板
   */
  async UpdateTemplate(template: RiskTemplate) {
    return await this.assist.Put<[]>(`${BaseUrl}/template`, {}, template);
  }

  /**
   * 克隆风控模板
   * @param template_id 用来克隆的模板id
   * @param template_name 克隆出的模板名称
   * @param isFull 是否全量克隆（为true代表同时复制基准模版的所有产品和账号的绑定关系）
   */
  async CloneTemplate(template_id: number, template_name: string, isFull: boolean) {
    return await this.assist.Get<[]>(`${BaseUrl}/template/clone`, { template_id, template_name, isFull });
  }

  /**
   * 删除风控模板
   */
  async DeleteTemplate(id: number) {
    return await this.assist.Delete<[]>(`${BaseUrl}/template`, { id });
  }

  /**
   * 绑定风控模板到各种实体
   */
  async BindTemplate2Identities(template_id: number, identities: number[] | string[]) {
    return await this.assist.Post<[]>(`${BaseUrl}/identity/bindIdentity`, { template_id }, identities);
  }

  /**
   * 查询本机构下所有的实体
   */
  async QueryAllEntities() {
    return await this.assist.Get<SystemIdentityInfo[]>(`${BaseUrl}/identity/all`);
  }

  /**
   * 查询某个实体所绑定的所有模板，以及每个模板下所包含的风控规则
   */
  async QueryIdentityTemplates(identity: number | string) {
    return await this.assist.Get<EntityRiskTemplateInfo[]>(`${BaseUrl}/fund/findByIdentity`, { identity });
  }

  /**
   * 查询所有风控指标
   */
  async QueryIndicators() {
    return await this.assist.Get<RiskIndicator[]>(`${BaseUrl}/indicator/all`);
  }

  /**
   * 查询所有风控规则
   */
  async QueryRules() {
    return await this.assist.Get<RiskRule[]>(`${BaseUrl}/rule/all`);
  }

  /**
   * 创建风控规则
   */
  async CreateRule(rule: RiskRule) {
    return await this.assist.Post<[]>(`${BaseUrl}/rule`, {}, rule);
  }

  /**
   * 更新风控规则
   */
  async UpdateRule(rule: RiskRule) {
    return await this.assist.Put<[]>(`${BaseUrl}/rule`, {}, rule);
  }

  /**
   * 删除风控规则
   */
  async DeleteRule(id: number) {
    return await this.assist.Delete<[]>(`${BaseUrl}/rule`, { id });
  }

  /**
   * 查询产品或账号上直接创建的规则
   * @param identity 主体id
   */
  async QueryRulesOnEntity(identity: number) {
    return await this.assist.Get<RiskRule[]>(`${BaseUrl}/rule/findByIdentity`, { identity });
  }

  /**
   * 在产品或账号上创建规则（所创建的规则，修改和删除调用原来的接口）
   * @param identity 主体id
   * @param identityType 主体类型
   */
  async CreateRuleOnEntity(identity: number, identityType: number, rule: RiskRule) {
    return await this.assist.Post<[]>(`${BaseUrl}/rule/customerBind`, { identity, identityType }, rule);
  }

  /**
   * 解绑某个模板上的某个规则，并创建一个新的模板关联给某个实体
   * @param template_id 所要解绑的规则，来源风控模板id
   * @param rule_id 风控规则id
   * @param identity 主体id
   */
  async UnbindRuleAndReconnect2Entity(template_id: number, rule_id: string, identity: number) {
    return await this.assist.Get<[]>(`${BaseUrl}/rule/cancelBindRule`, { template_id, rule_id, identity });
  }

  /**
   * 在指定实体下编辑某个模板下的规则，并另存为
   * @param template_id 所编辑的规则，来源风控模板id
   * @param template_name 将整体克隆产生的新风控模板名称
   * @param identity 主体id
   */
  async SaveRuleAsNewAndReBindTemplate(template_id: number, template_name: string, identity: number, rule: RiskRule) {
    return await this.assist.Post<[]>(`${BaseUrl}/template/editRule`, { template_id, template_name, identity }, rule);
  }

  /**
   * 查询资产范围
   */
  async QueryAssetScopes() {
    return await this.assist.Get<KindOfAsset[]>(`${BaseUrl}/kind/all`);
  }

  /**
   * 查询已产生的预警消息
   */
  async QueryRiskMessages() {
    return await this.assist.Get<RiskMessage[]>(`${BaseUrl}/message/all`);
  }
}
